import { useState, useRef } from 'react';
import { Sparkles, Image, Palette, Music, Wand2, Play, Pause, Volume2, Download } from 'lucide-react';
import assetGenerator from '../../services/assetGenerator';
import audioManager from '../../services/audioManager';

const stylePresets = [
  { id: 'pixel-art', name: 'Pixel Art', description: 'Retro 8-bit style', icon: '🎮' },
  { id: 'cartoon', name: 'Cartoon', description: 'Colorful and friendly', icon: '🎨' },
  { id: 'realistic', name: 'Realistic', description: 'Detailed and lifelike', icon: '📸' },
  { id: 'minimalist', name: 'Minimalist', description: 'Clean and simple', icon: '⚪' },
  { id: 'neon', name: 'Neon', description: 'Futuristic glow effects', icon: '🌟' }
];

const musicStyles = [
  { id: 'upbeat', name: 'Upbeat', description: 'Energetic and motivating' },
  { id: 'ambient', name: 'Ambient', description: 'Calm and atmospheric' },
  { id: 'retro', name: 'Retro', description: '8-bit chiptune style' },
  { id: 'epic', name: 'Epic', description: 'Dramatic orchestral' },
  { id: 'minimal', name: 'Minimal', description: 'Simple and clean' }
];

function AICustomization({ gameData, onComplete, loading }) {
  const [selectedStyle, setSelectedStyle] = useState('cartoon');
  const [selectedMusicStyle, setSelectedMusicStyle] = useState('upbeat');
  const [customPrompts, setCustomPrompts] = useState({
    visual: '',
    audio: ''
  });
  const [generationStatus, setGenerationStatus] = useState({
    assets: 'idle', // idle, generating, complete, error
    audio: 'idle'
  });
  const [generatedAssets, setGeneratedAssets] = useState(null);
  const [generatedAudio, setGeneratedAudio] = useState(null);
  const [audioPlaying, setAudioPlaying] = useState({
    music: false,
    soundEffects: {}
  });
  const audioRefs = useRef({
    music: null,
    soundEffects: {}
  });

  const handleStyleSelect = (styleId) => {
    setSelectedStyle(styleId);
    setGeneratedAssets(null); // Reset generated assets when style changes
  };

  const handleMusicStyleSelect = (styleId) => {
    setSelectedMusicStyle(styleId);
    setGeneratedAudio(null); // Reset generated audio when style changes
  };

  const handlePromptChange = (type, value) => {
    setCustomPrompts(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const toggleAudioPlayback = (audioType, audioUrl, effectId = null) => {
    if (audioType === 'music') {
      if (audioPlaying.music) {
        // Pause music
        if (audioRefs.current.music) {
          audioRefs.current.music.pause();
        }
        setAudioPlaying(prev => ({ ...prev, music: false }));
      } else {
        // Play music
        if (audioRefs.current.music) {
          audioRefs.current.music.pause();
        }

        const audio = new Audio(audioUrl);
        audio.loop = true;
        audio.volume = 0.7;

        audio.onended = () => {
          setAudioPlaying(prev => ({ ...prev, music: false }));
        };

        audio.onerror = () => {
          console.error('Error playing audio');
          setAudioPlaying(prev => ({ ...prev, music: false }));
        };

        audioRefs.current.music = audio;
        audio.play().then(() => {
          setAudioPlaying(prev => ({ ...prev, music: true }));
        }).catch(error => {
          console.error('Error playing audio:', error);
        });
      }
    } else if (audioType === 'soundEffect' && effectId) {
      if (audioPlaying.soundEffects[effectId]) {
        // Pause sound effect
        if (audioRefs.current.soundEffects[effectId]) {
          audioRefs.current.soundEffects[effectId].pause();
        }
        setAudioPlaying(prev => ({
          ...prev,
          soundEffects: { ...prev.soundEffects, [effectId]: false }
        }));
      } else {
        // Play sound effect
        if (audioRefs.current.soundEffects[effectId]) {
          audioRefs.current.soundEffects[effectId].pause();
        }

        const audio = new Audio(audioUrl);
        audio.volume = 0.8;

        audio.onended = () => {
          setAudioPlaying(prev => ({
            ...prev,
            soundEffects: { ...prev.soundEffects, [effectId]: false }
          }));
        };

        audio.onerror = () => {
          console.error('Error playing sound effect');
          setAudioPlaying(prev => ({
            ...prev,
            soundEffects: { ...prev.soundEffects, [effectId]: false }
          }));
        };

        audioRefs.current.soundEffects[effectId] = audio;
        audio.play().then(() => {
          setAudioPlaying(prev => ({
            ...prev,
            soundEffects: { ...prev.soundEffects, [effectId]: true }
          }));
        }).catch(error => {
          console.error('Error playing sound effect:', error);
        });
      }
    }
  };

  const downloadAudio = (audioUrl, filename) => {
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const generateAssets = async () => {
    if (!gameData.template) return;

    setGenerationStatus(prev => ({ ...prev, assets: 'generating' }));

    try {
      const result = await assetGenerator.generateAssetPack(
        gameData.template.id,
        selectedStyle,
        customPrompts.visual
      );

      if (result.success) {
        setGeneratedAssets(result.assetPack);
        setGenerationStatus(prev => ({ ...prev, assets: 'complete' }));

        // Set first asset as preview
        // Assets generated successfully - preview will be handled by parent component
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Asset generation failed:', error);
      setGenerationStatus(prev => ({ ...prev, assets: 'error' }));
    }
  };

  const generateAudio = async () => {
    if (!gameData.template) return;

    setGenerationStatus(prev => ({ ...prev, audio: 'generating' }));

    try {
      const result = await audioManager.generateGameAudio(
        gameData.template.id,
        selectedMusicStyle,
        customPrompts.audio
      );

      if (result.success) {
        setGeneratedAudio(result.audioPackage);
        setGenerationStatus(prev => ({ ...prev, audio: 'complete' }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Audio generation failed:', error);

      // For demo purposes, provide sample audio URLs
      console.log('Using demo audio for testing...');
      const demoAudio = {
        music: {
          url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Demo URL
          metadata: {
            duration: 30,
            generated: true
          }
        },
        soundEffects: {
          jump: {
            url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Demo URL
            metadata: { duration: 1 }
          },
          collect: {
            url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Demo URL
            metadata: { duration: 0.5 }
          }
        },
        enhanced: true
      };

      setGeneratedAudio(demoAudio);
      setGenerationStatus(prev => ({ ...prev, audio: 'complete' }));
    }
  };

  const handleContinue = () => {
    onComplete({
      assets: generatedAssets,
      audio: generatedAudio,
      customizations: {
        visualStyle: selectedStyle,
        musicStyle: selectedMusicStyle,
        prompts: customPrompts
      }
    });
  };

  const canContinue = generationStatus.assets === 'complete' && generationStatus.audio === 'complete';

  return (
    <div className="ai-customization">
      <div className="step-header">
        <h2>🤖 Enhanced AI-Powered Customization</h2>
        <p>Let our advanced AI generate unique visual assets and audio for your {gameData.template?.name} game using Google's Genkit framework.</p>
        <div className="ai-features">
          <span className="feature-badge">🎨 Real Image Generation</span>
          <span className="feature-badge">🎵 AI Music Creation</span>
          <span className="feature-badge">🔊 Smart Sound Effects</span>
        </div>
      </div>



      <div className="customization-sections">
        {/* Visual Style Section */}
        <div className="customization-section">
          <div className="section-header">
            <Image className="section-icon" />
            <h3>Visual Style</h3>
          </div>

          <div className="style-grid">
            {stylePresets.map((style) => (
              <div
                key={style.id}
                className={`style-card ${selectedStyle === style.id ? 'selected' : ''}`}
                onClick={() => handleStyleSelect(style.id)}
              >
                <div className="style-icon">{style.icon}</div>
                <h4>{style.name}</h4>
                <p>{style.description}</p>
              </div>
            ))}
          </div>

          <div className="custom-prompt">
            <label htmlFor="visual-prompt">Custom Visual Prompt (Optional)</label>
            <textarea
              id="visual-prompt"
              name="visual-prompt"
              placeholder="Describe any specific visual elements you want (e.g., 'make the character a robot', 'use purple and gold colors')"
              value={customPrompts.visual || ''}
              onChange={(e) => handlePromptChange('visual', e.target.value)}
              rows={3}
              autoComplete="off"
              spellCheck="true"
            />
          </div>

          <button
            className="generate-button"
            onClick={generateAssets}
            disabled={loading || generationStatus.assets === 'generating'}
          >
            <Wand2 className="button-icon" />
            {generationStatus.assets === 'generating' ? 'Generating Assets...' : 'Generate Visual Assets'}
          </button>

          {generationStatus.assets === 'complete' && generatedAssets && (
            <div className="generation-result">
              <div className="result-header">
                <Sparkles className="result-icon" />
                <span>Assets Generated Successfully!</span>
                {generatedAssets.enhanced && (
                  <span className="enhancement-badge">🚀 Enhanced with Genkit</span>
                )}
              </div>
              <div className="asset-preview">
                {Object.entries(generatedAssets.assets).map(([type, asset]) => (
                  <div key={type} className="asset-item">
                    <div className="asset-thumbnail">
                      {asset.placeholder && (
                        <img src={asset.placeholder} alt={`${type} preview`} />
                      )}
                      {asset.imageData && (
                        <div className="real-image-indicator">🎨 AI Generated</div>
                      )}
                    </div>
                    <span className="asset-type">{type}</span>
                    {asset.metadata?.generatedWithGenkit && (
                      <span className="genkit-indicator">✨ Genkit</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {generationStatus.assets === 'error' && (
            <div className="generation-error">
              <p>Failed to generate assets. Please try again.</p>
              <button onClick={generateAssets} className="retry-button">Retry</button>
            </div>
          )}
        </div>

        {/* Audio Style Section */}
        <div className="customization-section">
          <div className="section-header">
            <Music className="section-icon" />
            <h3>Audio Style</h3>
          </div>

          <div className="music-grid">
            {musicStyles.map((style) => (
              <div
                key={style.id}
                className={`music-card ${selectedMusicStyle === style.id ? 'selected' : ''}`}
                onClick={() => handleMusicStyleSelect(style.id)}
              >
                <h4>{style.name}</h4>
                <p>{style.description}</p>
              </div>
            ))}
          </div>

          <div className="custom-prompt">
            <label htmlFor="audio-prompt">Custom Audio Prompt (Optional)</label>
            <textarea
              id="audio-prompt"
              name="audio-prompt"
              placeholder="Describe the mood or style you want for the music and sound effects"
              value={customPrompts.audio || ''}
              onChange={(e) => handlePromptChange('audio', e.target.value)}
              rows={3}
              autoComplete="off"
              spellCheck="true"
            />
          </div>

          <button
            className="generate-button"
            onClick={generateAudio}
            disabled={loading || generationStatus.audio === 'generating'}
          >
            <Wand2 className="button-icon" />
            {generationStatus.audio === 'generating' ? 'Generating Audio...' : 'Generate Audio'}
          </button>

          {generationStatus.audio === 'complete' && generatedAudio && (
            <div className="generation-result">
              <div className="result-header">
                <Sparkles className="result-icon" />
                <span>Audio Generated Successfully!</span>
                {generatedAudio.enhanced && (
                  <span className="enhancement-badge">🎵 Enhanced with Genkit</span>
                )}
              </div>
              <div className="audio-preview">
                {/* Background Music Player */}
                {generatedAudio.music && (
                  <div className="audio-player">
                    <div className="audio-info">
                      <div className="audio-title">
                        <Music className="audio-icon" />
                        <span>Background Music</span>
                        {generatedAudio.music?.metadata?.generated && (
                          <span className="ai-generated-indicator">🤖 AI Generated</span>
                        )}
                      </div>
                      <div className="audio-meta">
                        <span className="audio-duration">
                          {generatedAudio.music?.metadata?.duration || 30}s loop
                        </span>
                        <span className="audio-style">
                          {selectedMusicStyle} style
                        </span>
                      </div>
                    </div>

                    <div className="audio-controls">
                      <button
                        className={`play-button ${audioPlaying.music ? 'playing' : ''}`}
                        onClick={() => toggleAudioPlayback('music', generatedAudio.music.url)}
                        title={audioPlaying.music ? 'Pause Music' : 'Play Music'}
                      >
                        {audioPlaying.music ? <Pause size={20} /> : <Play size={20} />}
                      </button>

                      <button
                        className="download-button"
                        onClick={() => downloadAudio(generatedAudio.music.url, 'background-music.mp3')}
                        title="Download Music"
                      >
                        <Download size={16} />
                      </button>

                      <div className="volume-indicator">
                        <Volume2 size={16} />
                        <span>70%</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Sound Effects Players */}
                {generatedAudio.soundEffects && Object.keys(generatedAudio.soundEffects).length > 0 && (
                  <div className="sound-effects-section">
                    <h4 className="section-title">
                      <span>Sound Effects</span>
                      <span className="effects-count">
                        {Object.keys(generatedAudio.soundEffects).length} effects
                      </span>
                      {generatedAudio.enhanced && (
                        <span className="genkit-indicator">✨ Genkit</span>
                      )}
                    </h4>

                    <div className="sound-effects-grid">
                      {Object.entries(generatedAudio.soundEffects).map(([effectId, effectData]) => (
                        <div key={effectId} className="sound-effect-item">
                          <div className="effect-info">
                            <span className="effect-name">
                              {effectId.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                            </span>
                            <span className="effect-duration">
                              {effectData.metadata?.duration || 1}s
                            </span>
                          </div>

                          <div className="effect-controls">
                            <button
                              className={`play-button small ${audioPlaying.soundEffects[effectId] ? 'playing' : ''}`}
                              onClick={() => toggleAudioPlayback('soundEffect', effectData.url, effectId)}
                              title={audioPlaying.soundEffects[effectId] ? 'Stop Effect' : 'Play Effect'}
                            >
                              {audioPlaying.soundEffects[effectId] ? <Pause size={14} /> : <Play size={14} />}
                            </button>

                            <button
                              className="download-button small"
                              onClick={() => downloadAudio(effectData.url, `${effectId}.mp3`)}
                              title="Download Effect"
                            >
                              <Download size={12} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {generationStatus.audio === 'error' && (
            <div className="generation-error">
              <p>Failed to generate audio. Please try again.</p>
              <button onClick={generateAudio} className="retry-button">Retry</button>
            </div>
          )}
        </div>
      </div>

      <div className="step-actions">
        <button
          className="continue-button"
          onClick={handleContinue}
          disabled={!canContinue || loading}
        >
          Continue to Parameter Tuning
        </button>

        {!canContinue && (
          <p className="continue-hint">
            Generate both visual assets and audio to continue
          </p>
        )}
      </div>
    </div>
  );
}

export default AICustomization;
