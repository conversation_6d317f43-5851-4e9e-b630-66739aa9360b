// Temporarily disable Genkit imports to fix page loading
// import { genkit } from 'genkit';
// import { googleAI } from '@genkit-ai/googleai';

// Fallback to direct Gemini API for now
import { GoogleGenerativeAI } from '@google/generative-ai';

class GenkitService {
  constructor() {
    // Initialize with direct Gemini API as fallback
    this.genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);
    this.generationCache = new Map();
    this.isGenkitAvailable = false; // Flag to indicate Genkit status
  }

  // Generate game assets with AI descriptions and enhanced placeholders
  async generateGameAssets(prompt, assetType, gameTemplate, style = 'cartoon') {
    try {
      console.log(`Generating ${assetType} for ${gameTemplate} in ${style} style...`);

      // Generate AI description first
      let description;
      try {
        const enhancedPrompt = this.buildImagePrompt(prompt, assetType, gameTemplate, style);
        const descriptionPrompt = `Create a detailed description for ${enhancedPrompt}. Include colors, shapes, style details, and visual characteristics.`;

        const model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
        const descriptionResult = await model.generateContent(descriptionPrompt);
        description = descriptionResult.response.text();
      } catch (error) {
        console.warn('AI description failed, using fallback:', error);
        description = `A ${style} style ${assetType} for ${gameTemplate} game with vibrant colors and engaging design.`;
      }

      // Generate enhanced placeholder based on AI description
      const placeholder = this.generateAdvancedPlaceholder(assetType, style, description);

      return {
        type: assetType,
        style: style,
        description: description,
        imageData: placeholder,
        placeholder: placeholder,
        specifications: this.extractAssetSpecs(description, style),
        metadata: {
          gameTemplate,
          assetType,
          style,
          timestamp: Date.now(),
          generated: true,
          fallback: false,
          aiGenerated: true
        }
      };

    } catch (error) {
      console.error('Error generating asset:', error);
      return this.getFallbackAsset(assetType, style);
    }
  }

  // Generate music and audio with safe approach to prevent crashes
  async generateGameAudio(gameTemplate, musicStyle, customPrompt = '') {
    console.log(`🎵 Starting audio generation for ${gameTemplate} in ${musicStyle} style...`);

    try {
      console.log('🎵 Step 1: Generating background music...');
      const backgroundMusic = await this.generateProceduralMusic({
        tempo: 120,
        duration: 10,
        style: musicStyle
      });
      console.log('🎵 Background music generated:', backgroundMusic);

      console.log('🎵 Step 2: Generating sound effects...');
      const soundEffects = await this.generateProceduralSFX({}, gameTemplate);
      console.log('🎵 Sound effects generated:', soundEffects);

      const result = {
        gameTemplate,
        musicStyle,
        timestamp: Date.now(),
        music: {
          specifications: { tempo: 120, style: musicStyle, simplified: true },
          audioData: backgroundMusic,
          url: backgroundMusic.url,
          playable: backgroundMusic.playable,
          metadata: {
            style: musicStyle,
            duration: 10,
            loop: true,
            generated: true,
            simplified: true
          }
        },
        soundEffects: soundEffects,
        success: true
      };

      console.log('🎵 Audio generation completed successfully:', result);
      return result;

    } catch (error) {
      console.error('🎵 Error generating audio:', error);
      console.error('🎵 Error stack:', error.stack);
      return this.getFallbackAudioPackage(gameTemplate);
    }
  }

  // Tune game parameters with natural language
  async tuneGameParameters(userRequest, currentParams, gameTemplate) {
    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });

      const tuningResult = await model.generateContent(`You are a game parameter tuning expert.

        User request: "${userRequest}"
        Current parameters: ${JSON.stringify(currentParams, null, 2)}
        Game template: ${gameTemplate}

        Modify the parameters based on the user's request. Return ONLY a JSON object with the updated parameters.
        Keep the same structure but adjust values appropriately.

        Examples:
        - "make it easier" → increase gaps, reduce speed, reduce obstacles
        - "make it harder" → decrease gaps, increase speed, add obstacles
        - "make it faster" → increase all speed-related parameters

        Return only valid JSON:`);

      let result;
      try {
        result = JSON.parse(tuningResult.response.text());
      } catch {
        // Fallback parsing
        const responseText = tuningResult.response.text();
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          result = JSON.parse(jsonMatch[0]);
        } else {
          result = currentParams;
        }
      }

      return {
        success: true,
        parameters: result,
        changes: this.getParameterChanges(currentParams, result),
        explanation: `Applied changes based on: "${userRequest}"`,
        shouldRefresh: true, // Flag to trigger game re-rendering
        timestamp: Date.now() // For cache busting
      };

    } catch (error) {
      console.error('Error tuning parameters:', error);
      return {
        success: false,
        error: error.message,
        parameters: currentParams
      };
    }
  }

  // Helper methods
  buildImagePrompt(prompt, assetType, gameTemplate, style) {
    const styleDescriptions = {
      'pixel-art': 'retro 8-bit pixel art style with blocky shapes and limited color palette',
      'cartoon': 'colorful cartoon style with bold outlines and vibrant colors',
      'realistic': 'realistic style with detailed textures and natural lighting',
      'minimalist': 'clean minimalist style with simple shapes and flat colors',
      'neon': 'futuristic neon style with glowing effects and dark backgrounds'
    };

    return `Create a ${assetType} for a ${gameTemplate} game in ${styleDescriptions[style] || style} style. ${prompt}. 
    The asset should be suitable for HTML5 canvas rendering, with clear visual elements and appropriate proportions for game use.`;
  }

  buildMusicPrompt(gameTemplate, musicStyle, customPrompt) {
    return `Generate background music for a ${gameTemplate} game in ${musicStyle} style. ${customPrompt}. 
    The music should loop seamlessly and enhance the gameplay experience without being distracting.`;
  }

  buildSFXPrompt(gameTemplate, musicStyle) {
    const sfxTypes = {
      'flappybird': ['jump', 'hit', 'score', 'gameOver'],
      'speedrunner': ['jump', 'land', 'collect', 'hit', 'powerUp'],
      'whackthemole': ['whack', 'miss', 'moleAppear', 'timeUp'],
      'match3': ['match', 'swap', 'cascade', 'powerUp', 'gameWin'],
      'crossyroad': ['move', 'carHorn', 'water', 'collect', 'gameOver']
    };

    const effects = sfxTypes[gameTemplate] || ['action', 'collect', 'hit', 'success'];

    return `Generate sound effect specifications for a ${gameTemplate} game in ${musicStyle} style. 
    Create specs for these sound effects: ${effects.join(', ')}. 
    Each effect should be distinct and appropriate for the game action.`;
  }

  generateAdvancedPlaceholder(assetType, style, description) {
    // Create more sophisticated placeholder based on AI description
    // Check if we're in a browser environment
    if (typeof document === 'undefined') {
      // Node.js environment - return a simple data URL
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }

    const canvas = document.createElement('canvas');
    canvas.width = 128;
    canvas.height = 128;
    const ctx = canvas.getContext('2d');

    // Parse description for colors and shapes
    const colors = this.extractColorsFromDescription(description);
    // const shapes = this.extractShapesFromDescription(description); // TODO: Use shapes for generation

    // Generate based on style
    switch (style) {
      case 'pixel-art':
        this.drawPixelArtPlaceholder(ctx, assetType, colors);
        break;
      case 'neon':
        this.drawNeonPlaceholder(ctx, assetType, colors);
        break;
      default:
        this.drawStandardPlaceholder(ctx, assetType, colors);
    }

    return canvas.toDataURL();
  }

  extractColorsFromDescription(description) {
    // Extract color words from description
    const colorWords = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan', 'gold', 'silver'];
    const foundColors = colorWords.filter(color =>
      description.toLowerCase().includes(color)
    );

    return foundColors.length > 0 ? foundColors : ['blue', 'white'];
  }

  extractShapesFromDescription(description) {
    const shapeWords = ['circle', 'square', 'triangle', 'diamond', 'star', 'hexagon'];
    return shapeWords.filter(shape =>
      description.toLowerCase().includes(shape)
    );
  }

  drawPixelArtPlaceholder(ctx, assetType, colors) {
    // Pixel art style placeholder
    ctx.imageSmoothingEnabled = false;
    const pixelSize = 8;

    for (let x = 0; x < 128; x += pixelSize) {
      for (let y = 0; y < 128; y += pixelSize) {
        if (Math.random() > 0.7) {
          ctx.fillStyle = this.getColorHex(colors[Math.floor(Math.random() * colors.length)]);
          ctx.fillRect(x, y, pixelSize, pixelSize);
        }
      }
    }
  }

  drawNeonPlaceholder(ctx, assetType, colors) {
    // Neon style placeholder
    ctx.fillStyle = '#000011';
    ctx.fillRect(0, 0, 128, 128);

    ctx.shadowBlur = 20;
    ctx.shadowColor = this.getColorHex(colors[0]);
    ctx.strokeStyle = this.getColorHex(colors[0]);
    ctx.lineWidth = 3;

    // Draw glowing outline
    ctx.strokeRect(20, 20, 88, 88);
  }

  drawStandardPlaceholder(ctx, assetType, colors) {
    // Standard placeholder
    const gradient = ctx.createLinearGradient(0, 0, 128, 128);
    gradient.addColorStop(0, this.getColorHex(colors[0]));
    gradient.addColorStop(1, this.getColorHex(colors[1] || colors[0]));

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 128, 128);
  }

  getColorHex(colorName) {
    const colorMap = {
      red: '#FF0000', blue: '#0000FF', green: '#00FF00', yellow: '#FFFF00',
      purple: '#800080', orange: '#FFA500', pink: '#FFC0CB', cyan: '#00FFFF',
      gold: '#FFD700', silver: '#C0C0C0', white: '#FFFFFF', black: '#000000'
    };
    return colorMap[colorName] || '#888888';
  }

  async generateProceduralMusic(specs) {
    // Safe audio generation without Web Audio API crashes
    console.log('Generating safe procedural music with specs:', specs);

    try {
      // Return a safe demo audio structure without complex generation
      return {
        audioBuffer: null,
        url: this.getSafeAudioUrl(),
        format: 'mp3',
        specifications: specs,
        playable: true,
        demo: true
      };
    } catch (error) {
      console.error('Audio generation failed:', error);
      return {
        audioBuffer: null,
        url: null,
        format: 'mp3',
        specifications: specs,
        playable: false,
        error: error.message
      };
    }
  }

  getSafeAudioUrl() {
    // Return a safe demo audio URL that won't crash
    // Using a simple data URL for a short beep sound
    return 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
  }

  generatePianoNote(time, tempo) {
    const frequency = 440 * Math.pow(2, Math.floor(time * tempo / 60) % 12 / 12);
    return Math.sin(2 * Math.PI * frequency * time) * Math.exp(-time % (60 / tempo) * 5);
  }

  generateDrumBeat(time, tempo) {
    const beatTime = time % (60 / tempo);
    if (beatTime < 0.1) {
      return (Math.random() - 0.5) * Math.exp(-beatTime * 20);
    }
    return 0;
  }

  generateBasicMelody(time, tempo) {
    const notes = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88]; // C major scale
    const noteIndex = Math.floor(time * tempo / 60) % notes.length;
    const frequency = notes[noteIndex];
    const noteTime = time % (60 / tempo);
    return Math.sin(2 * Math.PI * frequency * time) * Math.exp(-noteTime * 3) * 0.5;
  }

  generateEnvelope(time, duration) {
    const fadeIn = Math.min(time * 2, 1);
    const fadeOut = Math.min((duration - time) * 2, 1);
    return Math.min(fadeIn, fadeOut);
  }

  // Convert AudioBuffer to WAV blob for playback
  audioBufferToWav(buffer) {
    const length = buffer.length;
    const numberOfChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  async generateProceduralSFX(specs, gameTemplate) {
    // Generate safe sound effects without crashes
    console.log('Generating safe sound effects for:', gameTemplate);

    const effects = {};
    const commonEffects = ['jump', 'collect', 'hit', 'success'];

    // Generate safe demo effects
    for (const effectName of commonEffects) {
      effects[effectName] = {
        audioBuffer: null,
        url: this.getSafeAudioUrl(),
        format: 'wav',
        specifications: { duration: 0.5, frequency: 440, demo: true },
        playable: true,
        demo: true
      };
    }

    return effects;
  }

  bufferToDataURL(buffer) {
    // Convert AudioBuffer to data URL (simplified fallback)
    try {
      if (!buffer) {
        return 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
      }

      const wavBlob = this.audioBufferToWav(buffer);
      return URL.createObjectURL(wavBlob);
    } catch (error) {
      console.warn('Buffer to data URL conversion failed:', error);
      return 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
    }
  }

  getParameterChanges(oldParams, newParams) {
    const changes = [];

    Object.keys(newParams).forEach(key => {
      if (oldParams[key] !== newParams[key]) {
        changes.push({
          parameter: key,
          oldValue: oldParams[key],
          newValue: newParams[key],
          change: this.calculateChange(oldParams[key], newParams[key])
        });
      }
    });

    return changes;
  }

  calculateChange(oldValue, newValue) {
    if (typeof oldValue === 'number' && typeof newValue === 'number') {
      const percentChange = ((newValue - oldValue) / oldValue) * 100;
      return {
        type: 'numeric',
        absolute: newValue - oldValue,
        percentage: Math.round(percentChange * 100) / 100
      };
    }

    return {
      type: 'value',
      description: `Changed from ${oldValue} to ${newValue}`
    };
  }

  getFallbackAsset(assetType, style) {
    return {
      type: assetType,
      style: style,
      description: `Fallback ${assetType} asset in ${style} style`,
      imageData: null,
      placeholder: this.generateAdvancedPlaceholder(assetType, style, `${style} ${assetType}`),
      specifications: {
        style: style,
        colors: ['#888888'],
        resolution: '128x128',
        features: ['fallback']
      },
      metadata: {
        assetType,
        style,
        timestamp: Date.now(),
        fallback: true
      }
    };
  }

  getFallbackAudioPackage(gameTemplate) {
    return {
      gameTemplate,
      musicStyle: 'fallback',
      timestamp: Date.now(),
      music: {
        specifications: { description: 'Fallback music' },
        audioData: null,
        metadata: { fallback: true }
      },
      soundEffects: {},
      success: false,
      fallback: true
    };
  }
}

export default new GenkitService();
